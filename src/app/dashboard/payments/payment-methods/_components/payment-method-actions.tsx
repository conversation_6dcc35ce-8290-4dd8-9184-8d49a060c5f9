'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react';
import { toast } from 'sonner';

type PaymentMethod = {
  id: string;
  name: string;
  code: string;
  description: string | null;
  isActive: boolean;
  isDefault: boolean;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
};

type PaymentMethodActionsProps = {
  method: PaymentMethod;
};

export default function PaymentMethodActions({ method }: PaymentMethodActionsProps) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleToggleStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/payment-methods/${method.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !method.isActive,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update payment method');
      }

      toast.success(`Payment method ${method.isActive ? 'deactivated' : 'activated'} successfully`);
      router.refresh();
    } catch (error) {
      console.error('Error toggling payment method status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update payment method');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetDefault = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/payment-methods/${method.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isDefault: true,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to set default payment method');
      }

      toast.success('Default payment method updated successfully');
      router.refresh();
    } catch (error) {
      console.error('Error setting default payment method:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to set default payment method');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/payment-methods/${method.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete payment method');
      }

      toast.success('Payment method deleted successfully');
      router.refresh();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting payment method:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete payment method');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => router.push(`dashboard/payments/payment-methods/${method.id}/edit`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleToggleStatus} disabled={isLoading}>
            {method.isActive ? (
              <ToggleLeft className="mr-2 h-4 w-4" />
            ) : (
              <ToggleRight className="mr-2 h-4 w-4" />
            )}
            {method.isActive ? 'Deactivate' : 'Activate'}
          </DropdownMenuItem>
          {!method.isDefault && method.isActive && (
            <DropdownMenuItem onClick={handleSetDefault} disabled={isLoading}>
              <ToggleRight className="mr-2 h-4 w-4" />
              Set as Default
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the payment method
              "{method.name}" and remove it from all associated records.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
